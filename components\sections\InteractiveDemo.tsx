'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Message, ThemeMode, TimeFormat } from '../../types/message';
import PhonePreview from '../device/PhonePreview';
import { useTimeInput } from '../../hooks/useTimeInput';
import { 
  DEMO_SCENARIOS, 
  getDemoScenarioById,
  getRandomDemoScenario 
} from '../../utils/demoData';

// 演示步骤接口定义
interface DemoStep {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const InteractiveDemo: React.FC = () => {
  // 演示状态管理
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [selectedScenario, setSelectedScenario] = useState<string>('casual-chat');
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  
  // 应用状态（演示用）
  const [recipientName, setRecipientName] = useState<string>('演示联系人');
  const [recipientAvatar, setRecipientAvatar] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [mode, setMode] = useState<ThemeMode>('light');
  const [batteryPercentage, setBatteryPercentage] = useState<number>(85);
  
  // 时间管理
  const {
    deviceTime,
    timeFormat,
    handleFormattedTimeChange,
    handleTimeFormatChange
  } = useTimeInput();

  // 引用
  const phonePreviewRef = useRef<HTMLDivElement>(null);

  // 加载选中的演示场景
  useEffect(() => {
    const scenario = getDemoScenarioById(selectedScenario);
    if (scenario) {
      setRecipientName(scenario.recipientName);
      setMessages(scenario.messages);
      setMode(scenario.theme);
      setBatteryPercentage(scenario.batteryPercentage);
      handleFormattedTimeChange(scenario.deviceTime);
    }
  }, [selectedScenario, handleFormattedTimeChange]);

  // 标记步骤完成
  const markStepCompleted = useCallback((stepId: number) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));
  }, []);

  // 获取步骤图标
  const getStepIcon = (stepId: number): React.ReactNode => {
    const iconMap: { [key: number]: React.ReactNode } = {
      1: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      2: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      3: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      ),
      4: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      5: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      6: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      )
    };
    return iconMap[stepId] || null;
  };

  // 执行步骤动作
  const executeStepAction = useCallback((stepId: number) => {
    switch (stepId) {
      case 1:
        setRecipientName('张小明');
        break;
      case 2:
        const scenario = getDemoScenarioById(selectedScenario);
        if (scenario) setMessages(scenario.messages);
        break;
      case 3:
        // 角色指示器演示 - 这里可以添加高亮效果
        break;
      case 4:
        handleFormattedTimeChange('14:30');
        handleTimeFormatChange('24');
        break;
      case 5:
        setBatteryPercentage(42);
        break;
      case 6:
        setMode(mode === 'light' ? 'dark' : 'light');
        break;
    }
    markStepCompleted(stepId);
  }, [selectedScenario, mode, handleFormattedTimeChange, handleTimeFormatChange, markStepCompleted]);

  // 演示步骤定义
  const demoSteps: DemoStep[] = [
    {
      id: 1,
      title: '设置联系人信息',
      description: '首先设置联系人姓名，让对话看起来更真实',
      icon: getStepIcon(1),
      action: () => executeStepAction(1),
      completed: completedSteps.has(1)
    },
    {
      id: 2,
      title: '添加消息内容',
      description: '创建对话消息，支持文本和图片混合内容',
      icon: getStepIcon(2),
      action: () => executeStepAction(2),
      completed: completedSteps.has(2)
    },
    {
      id: 3,
      title: '使用角色指示器',
      description: '通过彩色圆点区分发送者和接收者消息',
      icon: getStepIcon(3),
      action: () => executeStepAction(3),
      completed: completedSteps.has(3)
    },
    {
      id: 4,
      title: '调整时间设置',
      description: '自定义设备时间显示和格式',
      icon: getStepIcon(4),
      action: () => executeStepAction(4),
      completed: completedSteps.has(4)
    },
    {
      id: 5,
      title: '调整电池电量',
      description: '设置设备电池显示百分比',
      icon: getStepIcon(5),
      action: () => executeStepAction(5),
      completed: completedSteps.has(5)
    },
    {
      id: 6,
      title: '切换主题模式',
      description: '在浅色和深色主题之间切换',
      icon: getStepIcon(6),
      action: () => executeStepAction(6),
      completed: completedSteps.has(6)
    }
  ];

  // 自动播放演示
  const startAutoDemo = useCallback(() => {
    setIsPlaying(true);
    setCurrentStep(0);
    setCompletedSteps(new Set());
    
    // 重置到默认场景
    const defaultScenario = getDemoScenarioById('casual-chat');
    if (defaultScenario) {
      setRecipientName(defaultScenario.recipientName);
      setMessages([]);
      setMode(defaultScenario.theme);
      setBatteryPercentage(defaultScenario.batteryPercentage);
    }
    
    // 开始自动执行步骤
    let stepIndex = 0;
    const executeStep = () => {
      if (stepIndex < demoSteps.length) {
        setCurrentStep(stepIndex);
        setTimeout(() => {
          demoSteps[stepIndex].action();
          stepIndex++;
          if (stepIndex < demoSteps.length) {
            setTimeout(executeStep, 2000);
          } else {
            setIsPlaying(false);
            setCurrentStep(demoSteps.length);
          }
        }, 500);
      }
    };
    
    executeStep();
  }, [demoSteps]);

  // 重置演示
  const resetDemo = useCallback(() => {
    setCurrentStep(0);
    setIsPlaying(false);
    setCompletedSteps(new Set());
    
    // 重置到选中的场景
    const scenario = getDemoScenarioById(selectedScenario);
    if (scenario) {
      setRecipientName(scenario.recipientName);
      setMessages(scenario.messages);
      setMode(scenario.theme);
      setBatteryPercentage(scenario.batteryPercentage);
      handleFormattedTimeChange(scenario.deviceTime);
    }
  }, [selectedScenario, handleFormattedTimeChange]);

  return (
    <section 
      id="interactive-demo"
      className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100"
      aria-labelledby="demo-title"
      role="region"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <h2 id="demo-title" className="text-3xl font-bold text-gray-900 mb-4">
            🎯 交互式操作演示
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            跟随下面的步骤，体验假短信生成器的强大功能。点击步骤或使用自动演示模式。
          </p>
        </div>
      </div>
    </section>
  );
};

export default InteractiveDemo;
