import { Message } from '../types/message';

// 演示场景接口定义
export interface DemoScenario {
  id: string;
  name: string;
  description: string;
  recipientName: string;
  messages: Message[];
  deviceTime: string;
  batteryPercentage: number;
  theme: 'light' | 'dark';
}

// 预定义的演示场景
export const DEMO_SCENARIOS: DemoScenario[] = [
  {
    id: 'casual-chat',
    name: '日常聊天',
    description: '朋友之间的轻松对话',
    recipientName: '小明',
    deviceTime: '10:30',
    batteryPercentage: 85,
    theme: 'light',
    messages: [
      {
        id: '1',
        sender: 'recipient',
        content: '你好！今天过得怎么样？',
        timestamp: '10:28',
        status: 'read'
      },
      {
        id: '2',
        sender: 'user',
        content: '很好！刚刚完成了一个重要的项目 🎉',
        timestamp: '10:29',
        status: 'delivered'
      },
      {
        id: '3',
        sender: 'recipient',
        content: '太棒了！我们一起庆祝一下吧',
        timestamp: '10:30',
        status: 'read'
      }
    ]
  },
  {
    id: 'work-discussion',
    name: '工作讨论',
    description: '同事间的工作交流',
    recipientName: '张经理',
    deviceTime: '14:15',
    batteryPercentage: 67,
    theme: 'light',
    messages: [
      {
        id: '1',
        sender: 'recipient',
        content: '下午的会议准备好了吗？',
        timestamp: '14:10',
        status: 'read'
      },
      {
        id: '2',
        sender: 'user',
        content: '是的，PPT已经准备完毕，会议室也预订好了',
        timestamp: '14:12',
        status: 'delivered'
      },
      {
        id: '3',
        sender: 'recipient',
        content: '很好，记得提前5分钟到场',
        timestamp: '14:13',
        status: 'read'
      },
      {
        id: '4',
        sender: 'user',
        content: '收到！',
        timestamp: '14:15',
        status: 'delivered'
      }
    ]
  },
  {
    id: 'family-chat',
    name: '家庭聊天',
    description: '家人之间的温馨对话',
    recipientName: '妈妈',
    deviceTime: '19:45',
    batteryPercentage: 42,
    theme: 'dark',
    messages: [
      {
        id: '1',
        sender: 'recipient',
        content: '今天晚饭想吃什么？',
        timestamp: '19:40',
        status: 'read'
      },
      {
        id: '2',
        sender: 'user',
        content: '红烧肉怎么样？好久没吃了',
        timestamp: '19:42',
        status: 'delivered'
      },
      {
        id: '3',
        sender: 'recipient',
        content: '好的，我去买点五花肉',
        timestamp: '19:43',
        status: 'read'
      },
      {
        id: '4',
        sender: 'user',
        content: '谢谢妈妈！❤️',
        timestamp: '19:45',
        status: 'delivered'
      }
    ]
  },
  {
    id: 'shopping-chat',
    name: '购物咨询',
    description: '在线购物客服对话',
    recipientName: '客服小助手',
    deviceTime: '15:20',
    batteryPercentage: 78,
    theme: 'light',
    messages: [
      {
        id: '1',
        sender: 'user',
        content: '请问这款手机有现货吗？',
        timestamp: '15:18',
        status: 'delivered'
      },
      {
        id: '2',
        sender: 'recipient',
        content: '您好！请问您需要哪个颜色和容量的呢？',
        timestamp: '15:19',
        status: 'read'
      },
      {
        id: '3',
        sender: 'user',
        content: '黑色，128GB的',
        timestamp: '15:19',
        status: 'delivered'
      },
      {
        id: '4',
        sender: 'recipient',
        content: '有现货的，现在下单可以享受9折优惠哦！',
        timestamp: '15:20',
        status: 'read'
      }
    ]
  },
  {
    id: 'appointment-reminder',
    name: '预约提醒',
    description: '医院预约提醒消息',
    recipientName: '市人民医院',
    deviceTime: '09:00',
    batteryPercentage: 95,
    theme: 'light',
    messages: [
      {
        id: '1',
        sender: 'recipient',
        content: '【预约提醒】您好，您预约的体检时间为今天上午10:00，请提前30分钟到达。',
        timestamp: '09:00',
        status: 'read'
      },
      {
        id: '2',
        sender: 'recipient',
        content: '地址：门诊楼3楼体检中心\n请携带身份证和预约单',
        timestamp: '09:00',
        status: 'read'
      }
    ]
  }
];

// 演示步骤数据
export interface DemoStepData {
  id: number;
  title: string;
  description: string;
  instruction: string;
  targetElement?: string;
  expectedResult: string;
  tips: string[];
}

export const DEMO_STEPS: DemoStepData[] = [
  {
    id: 1,
    title: '设置联系人信息',
    description: '为你的假短信对话设置一个真实的联系人姓名',
    instruction: '在"联系人姓名"输入框中输入一个姓名，比如"张小明"',
    targetElement: 'input[placeholder*="联系人"]',
    expectedResult: '联系人姓名会显示在iPhone预览的顶部',
    tips: [
      '使用真实的中文姓名会让对话看起来更自然',
      '避免使用过于复杂或特殊的字符',
      '姓名会实时显示在预览界面中'
    ]
  },
  {
    id: 2,
    title: '添加消息内容',
    description: '创建对话消息，支持多种消息类型',
    instruction: '点击消息输入区域，输入文本内容并选择发送者角色',
    expectedResult: '消息会以iPhone风格的气泡形式显示在预览中',
    tips: [
      '蓝色圆点代表用户发送的消息',
      '灰色圆点代表接收者的消息',
      '绿色圆点代表系统消息',
      '支持emoji表情和换行'
    ]
  },
  {
    id: 3,
    title: '使用角色指示器',
    description: '通过彩色圆点区分不同的消息发送者',
    instruction: '点击消息左侧的彩色圆点来切换消息发送者',
    expectedResult: '消息气泡的颜色和位置会根据角色改变',
    tips: [
      '用户消息显示在右侧，使用蓝色气泡',
      '接收者消息显示在左侧，使用灰色气泡',
      '系统消息居中显示，使用绿色气泡'
    ]
  },
  {
    id: 4,
    title: '上传图片内容',
    description: '为消息添加图片，创建更丰富的对话内容',
    instruction: '点击消息输入框右侧的图片图标，选择要上传的图片',
    expectedResult: '图片会显示在消息气泡中，支持文字和图片混合',
    tips: [
      '支持JPG、PNG、GIF、WebP格式',
      '图片大小建议不超过5MB',
      '图片会自动调整大小适配消息气泡',
      '可以同时包含文字和图片'
    ]
  },
  {
    id: 5,
    title: '调整时间设置',
    description: '自定义设备时间显示和格式',
    instruction: '在时间设置区域修改显示时间和时间格式',
    expectedResult: 'iPhone状态栏的时间会实时更新',
    tips: [
      '支持12小时制和24小时制',
      '时间格式会影响整个界面的显示',
      '可以设置任意时间来配合你的故事情节'
    ]
  },
  {
    id: 6,
    title: '调整电池电量',
    description: '设置设备电池显示百分比，增加真实感',
    instruction: '使用电池电量滑块调整显示的电量百分比',
    expectedResult: '状态栏的电池图标和百分比会更新',
    tips: [
      '电量范围从1%到100%',
      '低电量时电池图标会变红',
      '可以根据剧情需要设置合适的电量'
    ]
  },
  {
    id: 7,
    title: '切换主题模式',
    description: '在浅色和深色主题之间切换',
    instruction: '在主题设置中选择"浅色模式"或"深色模式"',
    expectedResult: '整个iPhone界面会切换到对应的主题色彩',
    tips: [
      '深色模式适合夜间场景',
      '浅色模式适合日间场景',
      '主题切换会影响所有界面元素'
    ]
  },
  {
    id: 8,
    title: '下载生成的图片',
    description: '将创建的假短信对话保存为高质量图片',
    instruction: '点击"下载图片"按钮保存你的作品',
    expectedResult: '生成的PNG图片会自动下载到你的设备',
    tips: [
      '图片为高分辨率PNG格式',
      '无水印，可自由使用',
      '适合用于视频制作、社交媒体等',
      '下载前可以滚动预览调整显示内容'
    ]
  }
];

// 获取随机演示场景
export const getRandomDemoScenario = (): DemoScenario => {
  const randomIndex = Math.floor(Math.random() * DEMO_SCENARIOS.length);
  return DEMO_SCENARIOS[randomIndex];
};

// 根据ID获取演示场景
export const getDemoScenarioById = (id: string): DemoScenario | undefined => {
  return DEMO_SCENARIOS.find(scenario => scenario.id === id);
};

// 获取演示步骤
export const getDemoStepById = (id: number): DemoStepData | undefined => {
  return DEMO_STEPS.find(step => step.id === id);
};

// 演示进度管理
export class DemoProgressManager {
  private completedSteps: Set<number> = new Set();
  private currentStep: number = 0;

  // 标记步骤完成
  markStepCompleted(stepId: number): void {
    this.completedSteps.add(stepId);
  }

  // 检查步骤是否完成
  isStepCompleted(stepId: number): boolean {
    return this.completedSteps.has(stepId);
  }

  // 获取完成的步骤数量
  getCompletedStepsCount(): number {
    return this.completedSteps.size;
  }

  // 获取总进度百分比
  getProgressPercentage(): number {
    return Math.round((this.completedSteps.size / DEMO_STEPS.length) * 100);
  }

  // 重置进度
  reset(): void {
    this.completedSteps.clear();
    this.currentStep = 0;
  }

  // 设置当前步骤
  setCurrentStep(step: number): void {
    this.currentStep = Math.max(0, Math.min(step, DEMO_STEPS.length - 1));
  }

  // 获取当前步骤
  getCurrentStep(): number {
    return this.currentStep;
  }

  // 获取下一个未完成的步骤
  getNextIncompleteStep(): number | null {
    for (let i = 0; i < DEMO_STEPS.length; i++) {
      if (!this.completedSteps.has(i + 1)) {
        return i + 1;
      }
    }
    return null;
  }
}
