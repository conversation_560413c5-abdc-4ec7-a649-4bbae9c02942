/**
 * 统一的消息类型定义
 * 用于整个应用中的消息数据结构
 */

export interface Message {
  id: string;
  sender: 'user' | 'recipient' | 'system';
  content: string;
  timestamp?: string;
  status?: 'default' | 'delivered' | 'failed' | 'read';
  imageUrl?: string;
  method?: 'data' | 'image' | 'unknown';
}

export interface DraftMessage {
  id: string;
  actor: 'sender' | 'recipient' | 'system';
  content: string;
  imageFile?: File;
  imageUrl?: string;
  timestamp?: string;
  status?: 'default' | 'delivered' | 'failed' | 'read';
  method: 'data' | 'image' | 'unknown';
}

export type TimeFormat = '12' | '24';
export type ThemeMode = 'light' | 'dark';
export type MessageSender = 'user' | 'recipient' | 'system';

export interface PhonePreviewProps {
  recipientName: string;
  recipientAvatar: string | null;
  messages: Message[];
  deviceTime: string;
  timeFormat: TimeFormat;
  mode: ThemeMode;
  batteryPercentage?: number; // 电池电量百分比 (0-100)
}

export interface MessageBubbleProps {
  message: Message;
  recipientName: string;
  recipientAvatar: string | null;
  mode: ThemeMode;
  timeFormat: TimeFormat;
  showTime?: boolean;
  isFirstMessage?: boolean; // 是否是第一个消息
  shouldShowTimestamp?: boolean; // 是否应该显示时间戳
}

// 角色配置类型
export interface RoleConfig {
  color: string;
  label: string;
  mappedSender: MessageSender;
}

export const ROLE_CONFIG: Record<string, RoleConfig> = {
  recipient: {
    color: 'rgb(209, 209, 211)', // Gray
    label: 'Recipient',
    mappedSender: 'recipient' as const
  },
  sender: {
    color: 'rgb(0, 122, 255)', // Blue
    label: 'User/Sender',
    mappedSender: 'user' as const
  },
  system: {
    color: 'rgb(52, 199, 89)', // Green
    label: 'System/Third Party',
    mappedSender: 'system' as const
  }
} as const;
